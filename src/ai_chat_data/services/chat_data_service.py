import re
import os
import sys
import copy
import json
import json_repair

from pathlib import Path
from fastapi import HTT<PERSON>Exception
from pydantic import BaseModel
from typing import *
from enum import Enum


from src.common.utils.logger import logger
from src.common.utils.config import config_data
from src.common.utils.serialize import serialize
from src.common.utils.extract_json import ej_agent
from src.common.utils.msg_utils import make_history
from src.common.utils.error_codes import return_error
from src.common.utils.time import get_current_time_with_weekday
from src.common.auth.mysql_utils import get_user_partial_info_from_db

from src.sql_engine.utils.db_config import DBConfig
from src.sql_engine.utils.db_mschema import MSchema
from src.sql_engine.utils.db_init import init_db_conn
from src.sql_engine.utils.db_source import HITLSQLDatabase

from src.ai_chat_data.utils.gather_fetch import (
    gather_datas_from_dirs,
    get_data_list_configs,
)
from src.ai_chat_data.utils.chat_data_utils import (
    match_metadata,
    get_jargon_infos,
    filter_tables,
    sql_few_shot_search,
)
from src.ai_chat_data.utils.talk_record_utils import update_data_used_time
from src.ai_chat_data.utils.talk_record_utils import update_talk_record

from src.ai_data_source.services.biz_relation_service import fetch_biz_relation
from src.ai_data_source.services.data_source_service import fetch_data_source_dy_id

from src.aagent.agent.aagent import AAgent
from src.aagent.utils.utils import random_uuid
from src.aagent.utils.load_prompt import load_prompt

current_dir = Path(__file__).resolve().parent.parent
prompt_dir = current_dir / "prompts"


async def _get_data_ids_set_list(user_id, dir_ids, data_ids):
    # 检索文件夹中的 data id，获取交集
    if dir_ids:
        sub_data_list = await gather_datas_from_dirs(user_id, dir_ids)
    else:
        sub_data_list = []
    data_list = list(set(data_ids) | set(sub_data_list))

    return data_list


async def _str_user_info(user_id: str):
    # 查询用户信息
    user_info = await get_user_partial_info_from_db(username=user_id)

    user_info_str = ""
    if user_realname := user_info.get("realname", ""):
        user_info_str += f"姓名: {user_realname}\n"
    if position_name := user_info.get("position_name", ""):
        user_info_str += f"职位: {position_name}\n"
    if depart_name := user_info.get("depart_name", ""):
        user_info_str += f"部门: {depart_name}\n"

    return user_info_str


async def _str_filter_data(data_infos, biz_infos):
    # 构造数据库信息
    filter_data_infos = ""
    for data_info in data_infos:
        if filter_data_infos:
            filter_data_infos += "\n\n"

        filter_data_infos += f'【DB_Name】 {data_info["data_config"]["data_name"]} ({data_info["data_config"]["data_type"]})\n'
        filter_data_infos += f'data_id: {data_info["data_id"]}\n'
        if rename := data_info.get("rename", ""):
            filter_data_infos += f"{rename}\n"
        if remark := data_info.get("remark", ""):
            filter_data_infos += f"{remark}\n"

        for item in biz_infos:
            if data_info["data_id"] == item["data_id"]:
                filter_data_infos += (
                    f'\n【相关业务】{item["biz_name"]}: {item["biz_desc"]}'
                )

    return filter_data_infos


async def _get_selected_data_infos(raw_ans, data_infos, user_ask):
    # _, block_sccess = await ej_agent.get_block(raw_ans)
    # if block_sccess:

    json_dict, json_sccess = await ej_agent.get_json(raw_ans)
    if json_sccess:
        if json_dict.get("save_user_ask", False):
            saved_user_ask = user_ask
        else:
            saved_user_ask = None

        data_id_list = json_dict.get("data_id_list", [])
        if data_id_list:
            data_info_dict = {
                data_info["data_id"]: {
                    k: v for k, v in data_info.items() if k != "mschema"
                }
                for data_info in data_infos
            }
            sorted_data_infos = [
                data_info_dict[data_id]
                for data_id in data_id_list
                if data_id in data_info_dict
            ]
        else:
            sorted_data_infos = None

        if sorted_data_infos:
            return {
                "saved_user_ask": saved_user_ask,
                "sorted_data_infos": sorted_data_infos,
            }
    return None


async def chat_select_data(
    user_id: str,
    data_ids: list,
    dir_ids: list,
    user_ask: str,
    qa_list: list,
    stream: bool = True,
    llm_data: dict | None = None,
):
    try:
        llm_data = llm_data or {}
        resp_raw = llm_data.get("resp_raw", {})
        data_list = llm_data.get("data_list", None)
        if data_list is None:
            data_list = await _get_data_ids_set_list(user_id, dir_ids, data_ids)
            llm_data["data_list"] = copy.deepcopy(data_list)

        current_time = llm_data.get("current_time", "")
        if not current_time:
            current_time = get_current_time_with_weekday()
            llm_data["current_time"] = current_time

        if len(data_list) == 1:
            # 无需进行意图识别
            data_info_res = await fetch_data_source_dy_id(
                user_id=user_id, data_id=data_list[0], mschema=True
            )

            if data_info_res["code"] != 200:
                raise HTTPException(
                    status_code=data_info_res["code"],
                    detail=data_info_res["msg"],
                )
            data_info = data_info_res["data"]["data_info"]

            llm_data["selected_data_id"] = copy.deepcopy(data_list[0])
            llm_data["selected_data_info"] = copy.deepcopy(data_info)

            if stream:
                # 无需传入 resp_raw
                return _stream_NS_agent(data_info=data_info, user_ask=user_ask)
            else:
                return await _nostream_NS_agent(data_info=data_info, user_ask=user_ask)

        history = copy.deepcopy(llm_data.get("history", []))
        if not history:
            if not qa_list:
                history = []
            else:
                history = await make_history(
                    talk_id=None,
                    qa_list=qa_list,
                    exclude_agent=["FD_agent", "FT_agent"],
                    check_talk_id=False,
                )
            llm_data["history"] = copy.deepcopy(history)

        user_info = llm_data.get("user_info", "")
        if not user_info:
            user_info = await _str_user_info(user_id)
            llm_data["user_info"] = user_info

        base_sys = llm_data.get("base_sys", "")
        if not base_sys:
            base_sys = await load_prompt(path=prompt_dir, name="base_sys")
            llm_data["base_sys"] = base_sys

        # 通过 id 个数判断是否进行数据库选择
        if len(data_list) > 1:
            data_infos = await get_data_list_configs(user_id, data_list)
            # 获取 biz 信息
            biz_infos = await fetch_biz_relation(
                user_id=user_id,
                page_size=0,
                page_number=0,
                key_word=None,
                enable_page=False,
            )
            biz_infos = biz_infos["data"]["biz_infos"]
            data_infos_str = await _str_filter_data(data_infos, biz_infos)

            # prompt
            filter_data_prompt = await load_prompt(path=prompt_dir, name="filter_data")
            filter_data_prompt = filter_data_prompt.format(
                data_infos=data_infos_str,
                user_info=user_info,
                user_ask=user_ask,
            )

            messages = copy.deepcopy(history)
            messages.append({"role": "user", "content": filter_data_prompt})

            FD_agent = AAgent(
                name="FD_agent",
                description="数据源选取",
                system=base_sys,
                model_config=config_data["llm"],
            )
            if stream:
                return _stream_FD_agent(
                    agent=FD_agent,
                    messages=messages,
                    data_infos=data_infos,
                    user_ask=user_ask,
                    resp_raw=resp_raw,
                )
            else:
                return await _nostream_FD_agent(
                    agent=FD_agent,
                    messages=messages,
                    data_infos=data_infos,
                    user_ask=user_ask,
                )
        else:
            # 不存在数据库
            chat_prompt = await load_prompt(path=prompt_dir, name="chat_ask_none")
            chat_prompt = chat_prompt.format(
                current_time=current_time,
                user_info=user_info,
            )

            messages = copy.deepcopy(history)
            messages.append({"role": "system", "content": chat_prompt})
            messages.append({"role": "user", "content": user_ask})

            ND_agent = AAgent(
                name="FD_agent",
                description="引导配置",
                system=base_sys,
                model_config=config_data["llm"],
            )

            if stream:
                return _stream_ND_agent(ND_agent, messages, resp_raw)
            else:
                return await _nostream_ND_agent(ND_agent, messages)
    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        if stream:

            async def generator(e):
                yield return_error(code=e.status_code, e=e)

            return generator(e)
        else:
            return return_error(code=e.status_code, e=e)


async def _stream_NS_agent(data_info, user_ask):
    resp_raw = {}
    new_msg_id = "chatcmpl-" + random_uuid()
    data_info.pop("mschema", None)

    resp_raw[new_msg_id] = {
        "name": "FD_agent",
        "description": "数据源选取",
        "ans": {"content": "", "role": "assistant"},
        "metadata": {
            "selected_data": {
                "code": 200,
                "data": {"saved_user_ask": user_ask, "sorted_data_infos": [data_info]},
                "msg": "成功选取数据源",
            }
        },
    }
    yield resp_raw


async def _nostream_NS_agent(data_info, user_ask):
    data_info.pop("mschema", None)
    return {
        "id": "chatcmpl-" + random_uuid(),
        "name": "FD_agent",
        "description": "数据源选取",
        "ans": {"content": "", "role": "assistant"},
        "metadata": {
            "selected_data": {
                "code": 200,
                "data": {"saved_user_ask": user_ask, "sorted_data_infos": [data_info]},
                "msg": "成功选取数据源",
            }
        },
    }


class SelectData(BaseModel):
    data_id_list: List[str]
    save_user_ask: bool


async def _stream_FD_agent(agent, messages, data_infos, user_ask, resp_raw):
    json_schema = SelectData.model_json_schema()

    resp = await agent.run_nonstream(
        messages,
        extra_body={"guided_json": json_schema},
    )
    raw_ans = resp["ans"]["content"]

    temp_id = resp.pop("id")
    resp_raw[temp_id] = resp

    selected_data = await _get_selected_data_infos(raw_ans, data_infos, user_ask)

    if selected_data:
        resp_raw[temp_id]["ans"]["content"] = ""
        resp_raw[temp_id]["metadata"]["selected_data"] = {
            "code": 200,
            "data": selected_data,
            "msg": "成功选取数据源",
        }
        yield resp_raw

    else:
        resp_raw[temp_id]["ans"]["content"] = raw_ans
        yield resp_raw


async def _nostream_FD_agent(agent, messages, data_infos, user_ask):
    json_schema = SelectData.model_json_schema()

    resp = await agent.run_nonstream(
        messages,
        extra_body={"guided_json": json_schema},
    )
    raw_ans = resp["ans"]["content"]

    selected_data = await _get_selected_data_infos(raw_ans, data_infos, user_ask)

    if selected_data:
        resp["ans"]["content"] = ""
        resp["metadata"]["selected_data"] = {
            "code": 200,
            "data": selected_data,
            "msg": "成功选取数据源",
        }

    return resp


async def _stream_ND_agent(agent, messages, resp_raw):
    async for chunk in agent.run(messages=messages):
        temp_id = chunk.pop("id")
        resp_raw[temp_id] = chunk

        yield resp_raw


async def _nostream_ND_agent(agent, messages):
    resp = await agent.run_nonstream(messages)
    return resp


async def _get_re_user_ask(raw_ans, user_ask):
    # re_user_ask = user_ask
    # _, block_sccess = await ej_agent.get_block(raw_ans)
    # if block_sccess:
    #     json_dict, json_sccess = await ej_agent.get_json(raw_ans)
    #     if json_sccess:
    #         re_user_ask = json_dict.get("re_user_ask", user_ask)

    return raw_ans


async def chat_ask_refactor(
    user_id: str,
    user_ask: str,
    data_id: str,
    qa_list: list,
    stream: bool = True,
    use_llm: bool = True,
    llm_data: dict = None,
):
    try:
        llm_data = llm_data or {}
        resp_raw = llm_data.get("resp_raw", {})
        history = copy.deepcopy(llm_data.get("history", []))
        if not history:
            if not qa_list:
                history = []
            else:
                history = await make_history(
                    talk_id=None,
                    qa_list=qa_list,
                    exclude_agent=["FD_agent", "FT_agent"],
                    check_talk_id=False,
                )
            llm_data["history"] = copy.deepcopy(history)

        current_time = llm_data.get("current_time", "")
        if not current_time:
            current_time = get_current_time_with_weekday()
            llm_data["current_time"] = current_time

        base_sys = llm_data.get("base_sys", "")
        if not base_sys:
            base_sys = await load_prompt(path=prompt_dir, name="base_sys")
            llm_data["base_sys"] = base_sys

        jargon_infos, re_user_ask = await get_jargon_infos(user_id, data_id, user_ask)
        jargon_infos = jargon_infos or "无"
        llm_data["jargon_infos"] = copy.deepcopy(jargon_infos)

        re_user_ask_prompt = await load_prompt(path=prompt_dir, name="re_user_ask")
        re_user_ask_prompt = re_user_ask_prompt.format(
            jargon_infos=jargon_infos,
            current_time=current_time,
            user_ask=re_user_ask,
        )

        messages = copy.deepcopy(history)
        messages.append({"role": "user", "content": re_user_ask_prompt})

        RA_agent = AAgent(
            name="RA_agent",
            description="问题改写",
            system=base_sys,
            model_config=config_data["llm"],
        )

        if stream:
            return _stream_RA_agent(
                agent=RA_agent,
                messages=messages,
                user_ask=re_user_ask,
                resp_raw=resp_raw,
                llm_data=llm_data,
                use_llm=use_llm,
            )
        else:
            return await _nostream_RA_agent(
                agent=RA_agent,
                messages=messages,
                user_ask=re_user_ask,
                llm_data=llm_data,
                use_llm=use_llm,
            )
    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        if stream:

            async def generator(e):
                yield return_error(code=e.status_code, e=e)

            return generator(e)
        else:
            return return_error(code=e.status_code, e=e)


async def _stream_RA_agent(
    agent,
    messages,
    user_ask,
    resp_raw,
    llm_data,
    use_llm,
):
    if use_llm:
        resp = await agent.run_nonstream(messages=messages)
        raw_ans = resp["ans"]["content"]

        re_user_ask = await _get_re_user_ask(raw_ans, user_ask)
        resp["ans"]["content"] = f"改写为: {re_user_ask}"
        resp_raw[resp.pop("id")] = resp

        llm_data["re_user_ask"] = re_user_ask

        yield resp_raw
    else:
        new_msg_id = "chatcmpl-" + random_uuid()
        resp_raw[new_msg_id] = {
            "name": agent.name,
            "description": agent.description,
            "ans": {
                "role": "assistant",
                "content": f"改写为: {user_ask}",
            },
        }
        llm_data["re_user_ask"] = user_ask

        yield resp_raw


async def _nostream_RA_agent(
    agent,
    messages,
    user_ask,
    llm_data,
    use_llm,
):
    if use_llm:
        resp = await agent.run_nonstream(messages=messages)
        raw_ans = resp["ans"]["content"]

        re_user_ask = await _get_re_user_ask(raw_ans, user_ask)
        llm_data["re_user_ask"] = re_user_ask
        resp["ans"]["content"] = f"改写为: {re_user_ask}"
        return resp

    else:
        llm_data["re_user_ask"] = user_ask

        return {
            "id": "chatcmpl-" + random_uuid(),
            "name": agent.name,
            "description": agent.description,
            "ans": {
                "role": "assistant",
                "content": f"改写为: {user_ask}",
            },
        }


async def _get_selected_tables(raw_ans, emb_table_list):
    selected_tables = emb_table_list
    # _, block_sccess = await ej_agent.get_block(raw_ans)
    # if block_sccess:

    json_dict, json_sccess = await ej_agent.get_json(raw_ans)
    if json_sccess:
        selected_tables = json_dict.get("selected_tables", emb_table_list)
        # 仅保留在 emb_table_list 中的表
        selected_tables = [
            table for table in selected_tables if table in emb_table_list
        ]

    return selected_tables


async def chat_select_table(
    user_id: str,
    user_ask: str,
    data_id: str,
    qa_list: list,
    stream: bool = True,
    llm_data: dict = None,
):
    try:
        llm_data = llm_data or {}
        resp_raw = llm_data.get("resp_raw", {})
        history = copy.deepcopy(llm_data.get("history", []))
        if not history:
            if not qa_list:
                history = []
            else:
                history = await make_history(
                    talk_id=None,
                    qa_list=qa_list,
                    exclude_agent=["FD_agent", "FT_agent"],
                    check_talk_id=False,
                )
            llm_data["history"] = copy.deepcopy(history)

        base_sys = llm_data.get("base_sys", "")
        if not base_sys:
            base_sys = await load_prompt(path=prompt_dir, name="base_sys")
            llm_data["base_sys"] = base_sys

        data_info = llm_data.get("selected_data_info", {})
        if not data_info or data_info.get("data_id", "") != data_id:
            data_info_res = await fetch_data_source_dy_id(
                user_id=user_id, data_id=data_id, mschema=True
            )
            if data_info_res["code"] != 200:
                raise HTTPException(
                    status_code=data_info_res["code"],
                    detail=data_info_res["msg"],
                )
            data_info = data_info_res["data"]["data_info"]

            llm_data["selected_data_id"] = copy.deepcopy(data_id)
            llm_data["selected_data_info"] = copy.deepcopy(data_info)

        filter_config = config_data.get("chat_data_table_filter", {})
        llm_filter = str(filter_config.get("llm_filter", "none")).lower().strip()

        emb_table_list = await filter_tables(user_id, user_ask, data_id, filter_config)

        if llm_filter == "all":
            data_mschema_dict = data_info.get("mschema", {})
            data_mschema = MSchema()
            data_mschema.load_from_dict(data_mschema_dict)
            table_mschema = data_mschema.to_mschema(selected_columns=[])

            final_num = filter_config.get("final_num", 3)

            filter_table_prompt = await load_prompt(
                path=prompt_dir, name="filter_table"
            )
            filter_table_prompt = filter_table_prompt.format(
                data_infos=table_mschema,
                final_num=final_num,
                user_ask=user_ask,
            )

            messages = copy.deepcopy(history)
            messages.append({"role": "user", "content": filter_table_prompt})

            FT_agent = AAgent(
                name="FT_agent",
                description="数据表选取",
                system=base_sys,
                model_config=config_data["llm"],
            )

            if stream:
                return _stream_FT_agent(
                    agent=FT_agent,
                    messages=messages,
                    emb_table_list=emb_table_list,
                    resp_raw=resp_raw,
                    llm_data=llm_data,
                )
            else:
                return await _nostream_FT_agent(
                    agent=FT_agent,
                    messages=messages,
                    emb_table_list=emb_table_list,
                    llm_data=llm_data,
                )

        # elif llm_filter == "alone":
        #     pass
        else:
            if stream:
                return _stream_FN_agent(
                    emb_table_list=emb_table_list,
                    resp_raw=resp_raw,
                    llm_data=llm_data,
                )
            else:
                return await _nostream_FN_agent(
                    emb_table_list=emb_table_list,
                    llm_data=llm_data,
                )

    except HTTPException as e:
        logger.error(return_error(code=e.status_code, e=e))
        if stream:

            async def generator(e):
                yield return_error(code=e.status_code, e=e)

            return generator(e)
        else:
            return return_error(code=e.status_code, e=e)


async def _stream_FN_agent(emb_table_list, resp_raw, llm_data):
    llm_data["selected_tables"] = emb_table_list
    new_msg_id = "chatcmpl-" + random_uuid()
    resp_raw[new_msg_id] = {
        "name": "FT_agent",
        "description": "数据表选取",
        "ans": {
            "role": "assistant",
            "content": "最终选择表为: " + ", ".join(emb_table_list),
        },
        "metadata": {
            "selected_tables": {
                "code": 200,
                "data": {"selected_tables": emb_table_list},
                "msg": "成功选取数据表",
            }
        },
    }

    yield resp_raw


async def _nostream_FN_agent(emb_table_list, llm_data):
    llm_data["selected_tables"] = emb_table_list
    return {
        "id": "chatcmpl-" + random_uuid(),
        "name": "FT_agent",
        "description": "数据表选取",
        "ans": {
            "role": "assistant",
            "content": "最终选择表为: " + ", ".join(emb_table_list),
        },
        "metadata": {
            "selected_tables": {
                "code": 200,
                "data": {"selected_tables": emb_table_list},
                "msg": "成功选取数据表",
            }
        },
    }


class SelectTable(BaseModel):
    selected_tables: List[str]


async def _stream_FT_agent(agent, messages, emb_table_list, resp_raw, llm_data):
    json_schema = SelectTable.model_json_schema()

    resp = await agent.run_nonstream(
        messages,
        extra_body={"guided_json": json_schema},
    )
    raw_ans = resp["ans"]["content"]

    temp_id = resp.pop("id")
    resp_raw[temp_id] = resp

    selected_tables = await _get_selected_tables(raw_ans, emb_table_list)

    if not resp_raw[temp_id]["ans"]["content"]:
        resp_raw[temp_id]["ans"]["content"] = "选择表为: " + ", ".join(selected_tables)

    llm_data["selected_tables"] = selected_tables

    resp_raw[temp_id]["metadata"]["selected_tables"] = {
        "code": 200,
        "data": {"selected_tables": selected_tables},
        "msg": "成功选取数据表",
    }

    yield resp_raw


async def _nostream_FT_agent(agent, messages, emb_table_list, llm_data):
    json_schema = SelectTable.model_json_schema()

    resp = await agent.run_nonstream(
        messages,
        extra_body={"guided_json": json_schema},
    )
    raw_ans = resp["ans"]["content"]

    selected_tables = await _get_selected_tables(raw_ans, emb_table_list)
    resp["ans"]["content"] = "选择表为: " + ", ".join(selected_tables)

    llm_data["selected_tables"] = selected_tables

    resp["metadata"]["selected_tables"] = {
        "code": 200,
        "data": {"selected_tables": selected_tables},
        "msg": "成功选取数据源",
    }

    return resp


async def chat_analysis(
    user_id: str,
    user_ask: str,
    data_id: str,
    qa_list: list,
    tables: list,
    stream: bool = True,
    llm_data: dict = None,
):
    try:
        llm_data = llm_data or {}
        resp_raw = llm_data.get("resp_raw", {})
        history = copy.deepcopy(llm_data.get("history", []))
        if not history:
            if not qa_list:
                history = []
            else:
                history = await make_history(
                    talk_id=None,
                    qa_list=qa_list,
                    exclude_agent=["FD_agent", "FT_agent"],
                    check_talk_id=False,
                )
            llm_data["history"] = copy.deepcopy(history)

        jargon_infos = llm_data.get("jargon_infos", "")
        if not llm_data.get("jargon_infos"):
            jargon_infos, user_ask = await get_jargon_infos(user_id, data_id, user_ask)
            jargon_infos = jargon_infos or "无"
            llm_data["jargon_infos"] = copy.deepcopy(jargon_infos)

        base_sys = llm_data.get("base_sys", "")
        if not base_sys:
            base_sys = await load_prompt(path=prompt_dir, name="base_sys")
            llm_data["base_sys"] = base_sys

        current_time = llm_data.get("current_time", "")
        if not current_time:
            current_time = get_current_time_with_weekday()
            llm_data["current_time"] = current_time

        data_info = llm_data.get("selected_data_info", {})
        if not data_info or data_info.get("data_id", "") != data_id:
            data_info_res = await fetch_data_source_dy_id(
                user_id=user_id, data_id=data_id, mschema=True
            )
            if data_info_res["code"] != 200:
                raise HTTPException(
                    status_code=data_info_res["code"],
                    detail=data_info_res["msg"],
                )
            data_info = data_info_res["data"]["data_info"]

            llm_data["selected_data_id"] = copy.deepcopy(data_id)
            llm_data["selected_data_info"] = copy.deepcopy(data_info)

        data_config = data_info.get("data_config", {})
        data_type = data_config["data_type"]

        data_mschema_dict = data_info.get("mschema", {})
        data_mschema = MSchema()
        data_mschema.load_from_dict(data_mschema_dict)
        mschema_str = data_mschema.to_mschema(selected_tables=tables)

        chart_type_str = await load_prompt(path=prompt_dir, name="chart_type")

        ##### sql 思路
        analysis_sql_prompt = await load_prompt(path=prompt_dir, name=f"analysis")
        analysis_sql_prompt = analysis_sql_prompt.format(
            data_infos=mschema_str,
            jargon_infos=jargon_infos,
            chart_type=chart_type_str,
            user_ask=user_ask,
            current_time=current_time,
        )

        messages = copy.deepcopy(history)
        messages.append({"role": "user", "content": analysis_sql_prompt})

        AS_agent = AAgent(
            name="AS_agent",
            description="思路分析",
            system=base_sys,
            model_config=config_data["llm"],
        )

        if stream:
            return _stream_AS_agent(
                agent=AS_agent,
                messages=messages,
                resp_raw=resp_raw,
                llm_data=llm_data,
            )
        else:
            return await _nostream_AS_agent(
                agent=AS_agent,
                messages=messages,
                llm_data=llm_data,
            )

    except HTTPException as e:
        logger.exception(return_error(code=e.status_code, e=e))
        if stream:

            async def generator(e):
                yield return_error(code=e.status_code, e=e)

            return generator(e)
        else:
            return return_error(code=e.status_code, e=e)


async def _stream_AS_agent(agent, messages, resp_raw, llm_data):

    async for chunk in agent.run(messages=messages):
        temp_id = chunk.pop("id")

        raw_ans = chunk["ans"]["content"]
        _metadata = match_metadata(raw_ans, (r"```(sql)?", "```"))

        before_text = _metadata["before_text"]
        after_text = _metadata["after_text"]

        resp_raw[temp_id] = chunk
        resp_raw[temp_id]["ans"]["content"] = before_text + after_text

        yield resp_raw

    llm_data["sql_analysis"] = resp_raw[temp_id]["ans"]["content"]


async def _nostream_AS_agent(agent, messages, llm_data):
    resp = await agent.run_nonstream(messages)
    raw_ans = resp["ans"]["content"]

    _metadata = match_metadata(raw_ans, (r"```(sql)?", "```"))

    before_text = _metadata["before_text"]
    after_text = _metadata["after_text"]
    resp["ans"]["content"] = before_text + after_text

    llm_data["sql_analysis"] = resp["ans"]["content"]

    return resp


async def _get_few_shot_str(user_id, data_id, user_ask, example_type="sql"):
    if example_type == "template":
        few_shot_num = 1
    else:
        few_shot_num = config_data.get("few_shot_num", 5)

    few_shot_list = await sql_few_shot_search(
        user_id=user_id,
        data_id=data_id,
        query=user_ask,
        example_type=example_type,
        k=few_shot_num,
    )

    few_shot_str = ""
    for item in few_shot_list:
        few_shot_str += f'Q: {item["user_ask"]}\n'
        few_shot_str += f'A: {item["example"]}\n\n'

    few_shot_prompt = ""
    if few_shot_str:
        few_shot_prompt += "以下是几个例子供你参考：\n"
        few_shot_prompt += few_shot_str
    return few_shot_prompt


async def _run_sql(data_conn, sql: str) -> dict:
    error = ""
    markdown_res = ""
    result_stats = {}

    result_rows = config_data.get("result_rows", 100)
    result = await data_conn.fetch_truncated_pd(sql, max_rows=result_rows)
    truncated_results = result["truncated_results"]
    data_frame = result["data_frame"]

    if data_frame is not None:
        result_stats = {
            col: {
                k: serialize(v, raise_error=False)
                for k, v in data_frame[col].describe().to_dict().items()
            }
            for col in data_frame.columns
        }
        data_frame = data_frame.map(serialize, raise_error=False)
        data_frame = json.loads(data_frame.to_json(orient="records", date_unit="s"))
        markdown_res = HITLSQLDatabase.trunc_result_to_markdown(result)
    else:
        error = truncated_results
    return {
        "sql": sql,
        "data_frame": data_frame,
        "markdown_res": markdown_res,
        "result_stats": result_stats,
        "error": error,
    }


async def chat_gen_sql(
    user_id: str,
    user_ask: str,
    data_id: str,
    qa_list: list,
    tables: list,
    return_ans=True,
    stream: bool = True,
    llm_data: dict = None,
):
    try:
        llm_data = llm_data or {}
        resp_raw = llm_data.get("resp_raw", {})
        history = copy.deepcopy(llm_data.get("history", []))
        if not history:
            if not qa_list:
                history = []
            else:
                history = await make_history(
                    talk_id=None,
                    qa_list=qa_list,
                    exclude_agent=["FD_agent", "FT_agent"],
                    check_talk_id=False,
                )
            llm_data["history"] = copy.deepcopy(history)

        jargon_infos = llm_data.get("jargon_infos", "")
        if not llm_data.get("jargon_infos"):
            jargon_infos, user_ask = await get_jargon_infos(user_id, data_id, user_ask)
            jargon_infos = jargon_infos or "无"
            llm_data["jargon_infos"] = copy.deepcopy(jargon_infos)

        base_sys = llm_data.get("base_sys", "")
        if not base_sys:
            base_sys = await load_prompt(path=prompt_dir, name="base_sys")
            llm_data["base_sys"] = base_sys

        current_time = llm_data.get("current_time", "")
        if not current_time:
            current_time = get_current_time_with_weekday()
            llm_data["current_time"] = current_time

        data_info = llm_data.get("selected_data_info", {})
        if not data_info or data_info.get("data_id", "") != data_id:
            data_info_res = await fetch_data_source_dy_id(
                user_id=user_id, data_id=data_id, mschema=True
            )
            if data_info_res["code"] != 200:
                raise HTTPException(
                    status_code=data_info_res["code"],
                    detail=data_info_res["msg"],
                )
            data_info = data_info_res["data"]["data_info"]

            llm_data["selected_data_id"] = copy.deepcopy(data_id)
            llm_data["selected_data_info"] = copy.deepcopy(data_info)

        sql_analysis = llm_data.get("sql_analysis", "")

        data_config = data_info.get("data_config", {})
        data_type = data_config["data_type"]

        data_mschema_dict = data_info.get("mschema", {})
        data_mschema = MSchema()
        data_mschema.load_from_dict(data_mschema_dict)
        mschema_str = data_mschema.to_mschema(selected_tables=tables)

        llm_data["data_mschema"] = data_mschema
        llm_data["mschema_str"] = mschema_str

        # 连接 data_conn
        db_config = DBConfig(
            db_type=data_config.get("data_type", None),
            db_path=data_config.get("data_path", None),
            db_name=data_config.get("data_name", None),
            db_host=data_config.get("data_host", None),
            port=data_config.get("data_port", None),
            user_name=data_config.get("data_user", None),
            db_pwd=data_config.get("data_pwd", None),
        )

        data_conn = init_db_conn(db_config, include_tables=tables, mschema=data_mschema)
        llm_data["data_conn"] = data_conn

        # 获取 few_shot
        few_shot_prompt = await _get_few_shot_str(user_id, data_id, user_ask)
        chart_type_str = await load_prompt(path=prompt_dir, name="chart_type")
        ##### sql 生成

        struct_sql_prompt = await load_prompt(
            path=prompt_dir, name=f"{data_type}_struct"
        )
        struct_sql_prompt = struct_sql_prompt.format(
            data_infos=mschema_str,
            jargon_infos=jargon_infos,
            sql_analysis=sql_analysis,
            chart_type=chart_type_str,
            few_shot=few_shot_prompt,
            current_time=current_time,
            user_ask=user_ask,
        )

        messages = copy.deepcopy(history)
        messages.append({"role": "user", "content": struct_sql_prompt})

        GS_agent = AAgent(
            name="GS_agent",
            description="SQL 生成",
            system=base_sys,
            model_config=config_data["llm"],
        )

        if stream:
            return _stream_GS_agent(
                agent=GS_agent,
                messages=messages,
                return_ans=return_ans,
                data_conn=data_conn,
                resp_raw=resp_raw,
                llm_data=llm_data,
            )
        else:
            return await _nostream_GS_agent(
                agent=GS_agent,
                messages=messages,
                return_ans=return_ans,
                data_conn=data_conn,
                llm_data=llm_data,
            )

    except HTTPException as e:
        logger.exception(return_error(code=e.status_code, e=e))
        if stream:

            async def generator(e):
                yield return_error(code=e.status_code, e=e)

            return generator(e)
        else:
            return return_error(code=e.status_code, e=e)


def remove_sql_code_block(text: str) -> str:
    # 使用正则表达式删除 ```sql 或 ```
    return re.sub(r"```sql|```", "", text)


async def _stream_GS_agent(agent, messages, return_ans, data_conn, resp_raw, llm_data):

    async for chunk in agent.run(messages=messages):
        temp_id = chunk.pop("id")

        raw_ans = chunk["ans"]["content"]
        resp_raw[temp_id] = chunk
        resp_raw[temp_id]["ans"]["content"] = ""

        yield resp_raw

    block_code, block_sccess = await ej_agent.get_block(raw_ans)

    if block_sccess:
        struct_sql = block_code
    else:
        struct_sql = remove_sql_code_block(raw_ans)

    sql_result = await _run_sql(data_conn, struct_sql)

    if return_ans:
        resp_raw[temp_id]["metadata"]["struct_sql"] = {
            "code": 200,
            "data": sql_result,
            "msg": "SQL 生成",
        }
    else:
        llm_data["candidate_sql"].append(copy.deepcopy(sql_result))
        resp_raw[temp_id]["metadata"]["struct_sql"] = {
            "code": 200,
            "data": {
                "sql": struct_sql,
            },
            "msg": "SQL 生成",
        }

    yield resp_raw


async def _nostream_GS_agent(agent, messages, return_ans, data_conn, llm_data):
    resp = await agent.run_nonstream(messages)
    raw_ans = resp["ans"]["content"]
    resp["ans"]["content"] = ""

    block_code, block_sccess = await ej_agent.get_block(raw_ans)

    if block_sccess:
        struct_sql = block_code
    else:
        struct_sql = remove_sql_code_block(raw_ans)

    sql_result = await _run_sql(data_conn, struct_sql)

    if return_ans:
        resp["metadata"]["struct_sql"] = {
            "code": 200,
            "data": sql_result,
            "msg": "SQL 生成",
        }
    else:
        llm_data["candidate_sql"].append(sql_result)
        resp["metadata"]["struct_sql"] = {
            "code": 200,
            "data": {
                "sql": struct_sql,
            },
            "msg": "SQL 生成",
        }

    return resp


def _get_rf_user_prompt(sql_result, user_ask):
    """
    生成SQL修复的用户提示词，每次只修复一个SQL

    Args:
        sql_result: 单个SQL执行结果字典，包含sql、error、result_stats等字段
        user_ask: 用户问题

    Returns:
        str: 格式化的用户提示词
    """
    user_prompt = f"【用户问题】\n{user_ask}\n\n"

    user_prompt += f"【待修复SQL】\n{sql_result['sql']}\n\n"

    if sql_result.get("error"):
        # SQL执行报错
        user_prompt += f"【错误信息】\n{sql_result['error']}\n\n"
        user_prompt += "【修复要求】\n请修复SQL语法错误，确保SQL能够正常执行。\n"
    elif sql_result.get("result_stats", {}).get("row_count", 0) == 0:
        # SQL执行成功但没有查询到结果
        user_prompt += "【执行结果】\nSQL执行成功，但查询结果为空。\n\n"
        user_prompt += "【修复要求】\n请分析为什么查询结果为空，并修复SQL以获得符合用户问题的查询结果。可能的原因包括：\n"
        user_prompt += "1. 查询条件过于严格\n2. 表连接条件不正确\n3. 字段名或表名错误\n4. 时间范围或其他筛选条件不合理\n"
    else:
        # SQL执行成功且有结果，但可能结果不符合用户需求
        result_stats = sql_result.get("result_stats", {})
        user_prompt += f"【执行结果】\nSQL执行成功，查询到 {result_stats.get('row_count', 0)} 行数据。\n\n"
        user_prompt += "【修复要求】\n请检查查询结果是否完全符合用户问题的需求。如果结果不符合用户需求，请修复SQL。可能需要调整：\n"
        user_prompt += "1. 查询字段是否完整和正确\n2. 聚合函数和分组条件\n3. 排序和限制条件\n4. 查询逻辑是否准确回答了用户问题\n"

    return user_prompt


async def _refine_sql_iteratively(
    user_id: str,
    user_ask: str,
    data_id: str,
    qa_list: list,
    tables: list,
    stream: bool = True,
    llm_data: dict = None,
):
    """
    迭代式SQL修复：每次只修复一个SQL，直到正确或达到最大修复次数

    Args:
        user_id: 用户ID
        user_ask: 用户问题
        data_id: 数据源ID
        qa_list: 问答历史
        tables: 表列表
        stream: 是否流式输出
        llm_data: LLM数据字典
    """
    if not llm_data.get("candidate_sql"):
        return

    max_refine_turns = config_data.get("refine_turns", 3)
    data_conn = llm_data.get("data_conn")

    for turn in range(max_refine_turns):
        current_sql = llm_data["candidate_sql"][0]

        # 检查当前SQL是否需要修复
        needs_fix = (
            current_sql.get("error") or  # 有语法错误
            current_sql.get("result_stats", {}).get("row_count", 0) == 0 or  # 结果为空
            not _is_sql_result_satisfactory(current_sql, user_ask)  # 结果不符合需求
        )

        if not needs_fix:
            # SQL已经正确，无需继续修复
            break

        # 执行SQL修复
        if stream:
            async for _ in await chat_refine_sql(
                user_id=user_id,
                user_ask=user_ask,
                data_id=data_id,
                qa_list=qa_list,
                tables=tables,
                sql_result=current_sql,
                return_ans=False,
                stream=True,
                llm_data=llm_data,
            ):
                if llm_data["resp_raw"]:
                    yield llm_data["resp_raw"]
        else:
            await chat_refine_sql(
                user_id=user_id,
                user_ask=user_ask,
                data_id=data_id,
                qa_list=qa_list,
                tables=tables,
                sql_result=current_sql,
                return_ans=False,
                stream=False,
                llm_data=llm_data,
            )

        # 修复后，用新的SQL结果替换原来的SQL
        if len(llm_data["candidate_sql"]) > 1:
            # 用最新修复的SQL替换第一个SQL
            llm_data["candidate_sql"][0] = llm_data["candidate_sql"][-1]
            # 移除最后一个SQL（避免重复）
            llm_data["candidate_sql"].pop()


def _is_sql_result_satisfactory(sql_result: dict, user_ask: str) -> bool:
    """
    判断SQL结果是否满足用户需求

    Args:
        sql_result: SQL执行结果
        user_ask: 用户问题

    Returns:
        bool: 是否满足需求
    """
    # 如果有错误，肯定不满足
    if sql_result.get("error"):
        return False

    # 如果结果为空，可能不满足需求（除非用户明确询问是否存在某些数据）
    row_count = sql_result.get("result_stats", {}).get("row_count", 0)
    if row_count == 0:
        # 简单的启发式判断：如果用户问题包含"是否"、"有没有"等词，空结果可能是正确的
        question_words = ["是否", "有没有", "存在", "包含"]
        if any(word in user_ask for word in question_words):
            return True
        return False

    # 其他情况暂时认为满足需求
    # 这里可以根据实际需求添加更复杂的判断逻辑
    return True


async def chat_refine_sql(
    user_id: str,
    user_ask: str,
    data_id: str,
    qa_list: list,
    tables: list,
    sql_result: dict,  # 改为单个SQL结果而不是列表
    return_ans=True,
    stream: bool = True,
    llm_data: dict = None,
):
    try:
        llm_data = llm_data or {}
        resp_raw = llm_data.get("resp_raw", {})
        history = copy.deepcopy(llm_data.get("history", []))
        if not history:
            if not qa_list:
                history = []
            else:
                history = await make_history(
                    talk_id=None,
                    qa_list=qa_list,
                    exclude_agent=["FD_agent", "FT_agent"],
                    check_talk_id=False,
                )
            llm_data["history"] = copy.deepcopy(history)

        jargon_infos = llm_data.get("jargon_infos", "")
        if not llm_data.get("jargon_infos"):
            jargon_infos, user_ask = await get_jargon_infos(user_id, data_id, user_ask)
            jargon_infos = jargon_infos or "无"
            llm_data["jargon_infos"] = copy.deepcopy(jargon_infos)

        base_sys = llm_data.get("base_sys", "")
        if not base_sys:
            base_sys = await load_prompt(path=prompt_dir, name="base_sys")
            llm_data["base_sys"] = base_sys

        data_info = llm_data.get("selected_data_info", {})
        if not data_info or data_info.get("data_id", "") != data_id:
            data_info_res = await fetch_data_source_dy_id(
                user_id=user_id, data_id=data_id, mschema=True
            )
            if data_info_res["code"] != 200:
                raise HTTPException(
                    status_code=data_info_res["code"],
                    detail=data_info_res["msg"],
                )
            data_info = data_info_res["data"]["data_info"]

            llm_data["selected_data_id"] = copy.deepcopy(data_id)
            llm_data["selected_data_info"] = copy.deepcopy(data_info)

        data_config = data_info.get("data_config", {})
        data_type = data_config["data_type"]

        data_mschema = llm_data.get("data_mschema", None)
        mschema_str = llm_data.get("mschema_str", None)
        if not mschema_str or not data_mschema:

            data_mschema_dict = data_info.get("mschema", {})
            data_mschema = MSchema()
            data_mschema.load_from_dict(data_mschema_dict)
            mschema_str = data_mschema.to_mschema(selected_tables=tables)

            llm_data["data_mschema"] = data_mschema
            llm_data["mschema_str"] = mschema_str

        # 连接 data_conn
        data_conn = llm_data.get("data_conn", None)
        if not data_conn:
            db_config = DBConfig(
                db_type=data_config.get("data_type", None),
                db_path=data_config.get("data_path", None),
                db_name=data_config.get("data_name", None),
                db_host=data_config.get("data_host", None),
                port=data_config.get("data_port", None),
                user_name=data_config.get("data_user", None),
                db_pwd=data_config.get("data_pwd", None),
            )

            data_conn = init_db_conn(
                db_config, include_tables=tables, mschema=data_mschema
            )

        ##### sql 修复
        rf_user_prompt = _get_rf_user_prompt(sql_result, user_ask)

        refine_sql_prompt = await load_prompt(
            path=prompt_dir, name=f"{data_type}_refine"
        )
        refine_sql_prompt = refine_sql_prompt.format(
            data_infos=mschema_str,
            jargon_infos=jargon_infos,
            current_time=get_current_time_with_weekday(),
        )

        messages = copy.deepcopy(history)
        messages.append({"role": "system", "content": refine_sql_prompt})
        messages.append({"role": "user", "content": rf_user_prompt})

        RF_agent = AAgent(
            name="RF_agent",
            description="SQL 纠错",
            system=base_sys,
            model_config=config_data["llm"],
        )

        if stream:
            return _stream_RF_agent(
                agent=RF_agent,
                messages=messages,
                return_ans=return_ans,
                data_conn=data_conn,
                resp_raw=resp_raw,
                llm_data=llm_data,
            )
        else:
            return await _nostream_RF_agent(
                agent=RF_agent,
                messages=messages,
                return_ans=return_ans,
                data_conn=data_conn,
                llm_data=llm_data,
            )

    except HTTPException as e:
        logger.exception(return_error(code=e.status_code, e=e))
        if stream:

            async def generator(e):
                yield return_error(code=e.status_code, e=e)

            return generator(e)
        else:
            return return_error(code=e.status_code, e=e)


async def _stream_RF_agent(agent, messages, return_ans, data_conn, resp_raw, llm_data):
    async for chunk in agent.run(messages=messages):
        temp_id = chunk.pop("id")

        raw_ans = chunk["ans"]["content"]
        resp_raw[temp_id] = chunk
        resp_raw[temp_id]["ans"]["content"] = ""

        yield resp_raw

    block_code, block_sccess = await ej_agent.get_block(raw_ans)

    if block_sccess:
        refine_sql = block_code
    else:
        refine_sql = remove_sql_code_block(raw_ans)

    refine_result = await _run_sql(data_conn, refine_sql)

    if return_ans:
        resp_raw[temp_id]["metadata"]["refine_sql"] = {
            "code": 200,
            "data": refine_result,
            "msg": "SQL 纠错",
        }
    else:
        llm_data["candidate_sql"].append(copy.deepcopy(refine_result))
        resp_raw[temp_id]["metadata"]["refine_sql"] = {
            "code": 200,
            "data": {
                "sql": refine_sql,
            },
            "msg": "SQL 纠错",
        }

    yield resp_raw


async def _nostream_RF_agent(agent, messages, return_ans, data_conn, llm_data):
    resp = await agent.run_nonstream(messages)
    raw_ans = resp["ans"]["content"]
    resp["ans"]["content"] = ""

    block_code, block_sccess = await ej_agent.get_block(raw_ans)

    if block_sccess:
        refine_sql = block_code
    else:
        refine_sql = remove_sql_code_block(raw_ans)

    refine_result = await _run_sql(data_conn, refine_sql)

    if return_ans:
        resp["metadata"]["refine_sql"] = {
            "code": 200,
            "data": refine_result,
            "msg": "SQL 纠错",
        }
    else:
        llm_data["candidate_sql"].append(refine_result)
        resp["metadata"]["refine_sql"] = {
            "code": 200,
            "data": {
                "sql": refine_sql,
            },
            "msg": "SQL 纠错",
        }

    return resp


def _get_select_user_prompt(sql_list):
    user_prompt = ""
    i = 0
    for sql_res in sql_list:
        i += 1
        user_prompt += f"【第{i}次生成】\n"
        user_prompt += f"    【SQL】\n{sql_res['sql']}\n"
        if sql_res["error"]:
            user_prompt += f"    【错误】\n{sql_res['error']}\n"
        else:
            user_prompt += f"    【结果统计】\n{sql_res['result_stats']}\n"

    return user_prompt


async def _get_selected_sql(raw_ans, sql_list):
    # _, block_sccess = await ej_agent.get_block(raw_ans)
    # if block_sccess:

    json_dict, json_sccess = await ej_agent.get_json(raw_ans)
    if json_sccess:
        index = int(json_dict.get("index", 1)) - 1
        chart_type = json_dict.get("chart_type", "Table")

        try:
            final_sql_result = sql_list[index]
        except:
            final_sql_result = sql_list[-1]

        if not final_sql_result.get("error"):  # 有错误
            for item in reversed(sql_list):
                if item.get("error"):  # 没有错误
                    final_sql_result = item
                    break  # 找到后立即退出循环

        return {
            **final_sql_result,
            "chart_type": chart_type,
        }
    return None


async def chat_select_sql(
    user_id: str,
    user_ask: str,
    sql_list: list,
    stream: bool = True,
    llm_data: dict = None,
):
    try:
        llm_data = llm_data or {}
        resp_raw = llm_data.get("resp_raw", {})
        base_sys = llm_data.get("base_sys", "")
        if not base_sys:
            base_sys = await load_prompt(path=prompt_dir, name="base_sys")
            llm_data["base_sys"] = base_sys

        data_info = llm_data.get("selected_data_info", {})
        data_config = data_info.get("data_config", {})
        data_type = data_config["data_type"]

        ##### sql 选择
        user_select_prompt = _get_select_user_prompt(sql_list)
        chart_type = await load_prompt(path=prompt_dir, name="chart_type")

        select_sql_prompt = await load_prompt(path=prompt_dir, name=f"select_sql")
        select_sql_prompt = select_sql_prompt.format(
            user_ask=user_ask,
            chart_type=chart_type,
            sql_list=user_select_prompt,
        )

        messages = []
        messages.append({"role": "user", "content": select_sql_prompt})

        SS_agent = AAgent(
            name="SS_agent",
            description="SQL 图表",
            system=base_sys,
            model_config=config_data["llm"],
        )

        if stream:
            return _stream_SS_agent(
                agent=SS_agent,
                messages=messages,
                sql_list=sql_list,
                resp_raw=resp_raw,
                llm_data=llm_data,
            )
        else:
            return await _nostream_SS_agent(
                agent=SS_agent,
                messages=messages,
                sql_list=sql_list,
                llm_data=llm_data,
            )

    except HTTPException as e:
        logger.exception(return_error(code=e.status_code, e=e))
        if stream:

            async def generator(e):
                yield return_error(code=e.status_code, e=e)

            return generator(e)
        else:
            return return_error(code=e.status_code, e=e)


class ChartType(str, Enum):
    comparison = "Comparison"
    trend = "Trend"
    distribution = "Distribution"
    rank = "Rank"
    proportion = "Proportion"
    composition = "Composition"
    relation = "Relation"
    anomaly = "Anomaly"
    indicator_board = "IndicatorBoard"
    table = "Table"


class SelectAns(BaseModel):
    index: int
    chart_type: ChartType


async def _stream_SS_agent(
    agent,
    messages,
    sql_list,
    resp_raw,
    llm_data,
):
    json_schema = SelectAns.model_json_schema()

    resp = await agent.run_nonstream(
        messages=messages,
        extra_body={"guided_json": json_schema},
    )

    raw_ans = resp["ans"]["content"]
    resp["ans"]["content"] = ""

    temp_id = resp.pop("id")
    resp_raw[temp_id] = resp

    final_sql_result = await _get_selected_sql(raw_ans, sql_list)
    if final_sql_result:
        llm_data["final_sql_result"] = copy.deepcopy(final_sql_result)
        if final_sql_result.get("error"):
            resp_raw[temp_id]["ans"][
                "content"
            ] = f'错误：{final_sql_result["error"]}\nSQL 运行失败，请重新提问。'
        else:
            resp_raw[temp_id]["metadata"]["final_sql_result"] = {
                "code": 200,
                "data": {
                    "sql": final_sql_result["sql"],
                    "data_frame": final_sql_result["data_frame"],
                    "markdown_res": final_sql_result["markdown_res"],
                    "result_stats": final_sql_result["result_stats"],
                    "error": final_sql_result["error"],
                    "chart_type": final_sql_result["chart_type"],
                },
                "msg": "SQL 选择",
            }
    else:
        resp_raw[temp_id]["ans"]["content"] = f"SQL 运行失败，请重新提问。"

    yield resp_raw


async def _nostream_SS_agent(
    agent,
    messages,
    sql_list,
    llm_data,
):
    json_schema = SelectAns.model_json_schema()

    resp = await agent.run_nonstream(
        messages=messages,
        extra_body={"guided_json": json_schema},
    )
    raw_ans = resp["ans"]["content"]
    resp["ans"]["content"] = ""

    final_sql_result = await _get_selected_sql(raw_ans, sql_list)

    if final_sql_result:
        llm_data["final_sql_result"] = copy.deepcopy(final_sql_result)

        if final_sql_result.get("error"):
            resp["ans"][
                "content"
            ] = f'错误：{final_sql_result["error"]}\nSQL 运行失败，请重新提问。'
        else:
            resp["metadata"]["final_sql_result"] = {
                "code": 200,
                "data": {
                    "final_sql": final_sql_result["sql"],
                    "final_result": final_sql_result["data_frame"],
                    "chart_type": final_sql_result["chart_type"],
                },
                "msg": "SQL 选择",
            }
    else:
        resp["ans"]["content"] = "SQL 运行失败，请重新提问。"

    return resp


async def chat_summary_sql(
    user_id: str,
    data_id: str,
    user_ask: str,
    stream: bool = True,
    llm_data: dict = None,
):
    try:
        llm_data = llm_data or {}
        resp_raw = llm_data.get("resp_raw", {})
        base_sys = llm_data.get("base_sys", "")
        if not base_sys:
            base_sys = await load_prompt(path=prompt_dir, name="base_sys")
            llm_data["base_sys"] = base_sys

        jargon_infos = llm_data.get("jargon_infos", "")
        if not llm_data.get("jargon_infos"):
            jargon_infos, user_ask = await get_jargon_infos(user_id, data_id, user_ask)
            jargon_infos = jargon_infos or "无"
            llm_data["jargon_infos"] = copy.deepcopy(jargon_infos)

        current_time = llm_data.get("current_time", "")
        if not current_time:
            current_time = get_current_time_with_weekday()
            llm_data["current_time"] = current_time

        ##### 总结
        final_sql_result = copy.deepcopy(llm_data["final_sql_result"])
        markdown_res = final_sql_result.get("markdown_res", "")
        result_stats = final_sql_result.get("result_stats", "")
        sql = final_sql_result.get("sql", "")

        summary_sql_prompt = await load_prompt(path=prompt_dir, name=f"summary")
        summary_sql_prompt = summary_sql_prompt.format(
            user_ask=user_ask,
            sql=sql,
            markdown_res=markdown_res,
            result_stats=result_stats,
            jargon_infos=jargon_infos,
            current_time=current_time,
        )

        messages = []
        messages.append({"role": "user", "content": summary_sql_prompt})

        SR_agent = AAgent(
            name="SR_agent",
            description="总结分析",
            system=base_sys,
            model_config=config_data["llm"],
        )

        if stream:
            return _stream_SR_agent(
                agent=SR_agent,
                messages=messages,
                resp_raw=resp_raw,
            )
        else:
            pass

    except HTTPException as e:
        logger.exception(return_error(code=e.status_code, e=e))
        if stream:

            async def generator(e):
                yield return_error(code=e.status_code, e=e)

            return generator(e)
        else:
            return return_error(code=e.status_code, e=e)


async def _stream_SR_agent(
    agent,
    messages,
    resp_raw,
):
    async for chunk in agent.run(messages=messages):
        temp_id = chunk.pop("id")
        resp_raw[temp_id] = chunk

        yield resp_raw


from src.ai_data_source.services.semantic_layer_service import fetch_table_list
from src.ai_data_source.services.semantic_layer_service import fetch_column_list
from src.common.utils.msg_utils import create_user_resp


async def chat_data(
    user_id: str,
    data_ids: list,
    dir_ids: list,
    user_ask: str,
    talk_id: str,
    qa_list: list,
    stream: bool = True,
    chat_prompt: str = None,
):
    try:
        resp_raw = await create_user_resp(user_ask=user_ask)
        llm_data = {"resp_raw": resp_raw, "candidate_sql": []}

        # 仅保留最近 history_num 个 qa
        history_num = config_data.get("history_num", 1)
        qa_list = qa_list[-int(history_num) :]

        if stream:
            return _stream_chat_data(
                user_id=user_id,
                data_ids=data_ids,
                dir_ids=dir_ids,
                talk_id=talk_id,
                user_ask=user_ask,
                qa_list=qa_list,
                llm_data=llm_data,
                chat_prompt=chat_prompt,
            )
        else:
            await _nostream_get_data(
                user_id=user_id,
                data_ids=data_ids,
                dir_ids=dir_ids,
                talk_id=talk_id,
                user_ask=user_ask,
                qa_list=qa_list,
                llm_data=llm_data,
            )
            if final_sql_result := llm_data.get("final_sql_result", None):

                final_resp = {
                    "code": 200,
                    "data": {"final_sql_result": final_sql_result},
                    "msg": "SQL 运行成功",
                }
            else:
                final_resp = {
                    "code": 500,
                    "data": None,
                    "msg": "SQL 运行失败，请重新提问",
                }
            return final_resp

    except Exception as e:
        logger.error(return_error(code=e.status_code, e=e))
    finally:
        if data_conn := llm_data.get("data_conn", None):
            data_conn.close()


def _str_data_info(data_info: dict):
    db_info_str = f'{data_info["data_config"]["data_name"]}\n'
    db_info_str += f'中文名: {data_info["rename"]}\n'
    db_info_str += f'备注: \n{data_info["remark"]}'
    return db_info_str


async def _stream_chat_data(
    user_id: str,
    data_ids: list,
    dir_ids: list,
    talk_id: str,
    user_ask: str,
    qa_list: list,
    llm_data: dict,
    chat_prompt: str = None,
):
    # 初始化 qa 记录
    await update_talk_record(user_id, data_ids, dir_ids, talk_id)

    # 准备数据
    resp_raw = llm_data.get("resp_raw", {})

    # 时间
    current_time = get_current_time_with_weekday()
    llm_data["current_time"] = current_time

    # 背景提示词
    base_sys = await load_prompt(path=prompt_dir, name="base_sys")
    llm_data["base_sys"] = base_sys

    # 对话记录
    if not qa_list:
        history = []
    else:
        history = await make_history(
            talk_id=None,
            qa_list=qa_list,
            exclude_agent=["FD_agent", "FT_agent"],
            check_talk_id=False,
        )
    llm_data["history"] = copy.deepcopy(history)

    # 用户信息
    user_info = await _str_user_info(user_id)
    llm_data["user_info"] = user_info

    # 检测选择的数据源的个数，以此设置 agent 可以调用的函数

    data_list = await _get_data_ids_set_list(user_id, dir_ids, data_ids)
    llm_data["data_list"] = copy.deepcopy(data_list)
    if len(data_list) == 1:
        # 已经确定单个数据源的情况
        selected_data_id = llm_data["selected_data_id"] = data_list[0]

        # 获取 data_info
        data_info_res = await fetch_data_source_dy_id(
            user_id=user_id, data_id=data_list[0], mschema=True
        )
        if data_info_res["code"] != 200:
            raise HTTPException(
                status_code=data_info_res["code"],
                detail=data_info_res["msg"],
            )
        selected_data_info = data_info_res["data"]["data_info"]
        llm_data["selected_data_info"] = copy.deepcopy(selected_data_info)

        # 构造 db_info_str
        db_info_str = _str_data_info(selected_data_info)

        await update_data_used_time(user_id=user_id, data_id=selected_data_id)

        jargon_infos, user_ask = await get_jargon_infos(
            user_id, selected_data_id, user_ask
        )
        jargon_infos = jargon_infos or "无"
        llm_data["jargon_infos"] = copy.deepcopy(jargon_infos)

        # 构造 get_data
        def get_data(query: str):
            """根据自然语言查询自动生成 SQL 语句并返回数据库结果。
            该函数使用自然语言的查询文本，自动解析并选择相关的数据表，生成对应的 SQL 查询语句，并执行查询返回结果。
            无需额外了解数据库结构，`get_data` 会自动处理所需的表和字段信息。
            此函数同时能获取用户的某些行业信息，因此无需确认问题的细节，无需确认数据库是否含有相关表格，直接调用即可。

            注意: 该函数独立处理数据库结构的获取，无需调用 get_table_info 或 get_column_info。

            Args:
                query: 用户输入的自然语言查询文本，例如 获取所有用户的姓名和邮箱 或 查找订单中价格大于100的商品
            """
            return query

        async def get_table_info(
            page_size: int = 10,
            page_number: int = 1,
            key_word: str = "",
        ):
            """获取指定数据库中符合条件的表的基本信息，包括表名、表的描述等。支持分页查询和关键词筛选。
            该函数主要用于查询数据库中表的 schema ，可以通过它了解数据库的表结构。

            Args:
                page_size: 每页返回的表信息数量. Defaults to 10.
                page_number: 当前页码，用于分页查询，结合 `page_size` 使用. Defaults to 1.
                key_word: 搜索关键词，用于模糊匹配表名或描述信息，空字符串表示不进行筛选，该参数应该在用户有明确需求时才使用. Defaults to "".
            """
            table_data = await fetch_table_list(
                user_id=user_id,
                data_id=selected_data_id,
                page_size=page_size,
                page_number=page_number,
                enable_page=True,
                key_word=key_word,
            )
            table_data = table_data.get("data", {})
            for item in table_data.get("table_infos", []):
                # 去除无关信息
                del item["data_name"]
                del item["desc_gen_status"]
                del item["example_sync_status"]
                del item["statistics_sync_status"]

            return table_data

        async def get_column_info(
            table_name: str,
        ):
            """该函数用于获取指定数据库中指定的表的字段的基本信息，包括字段名称、字段类型等。
            该函数用于查询某个表的字段 schema ，帮助了解表的字段结构。

            Args:
                table_name: 要获取其字段信息的表的名称.
            """
            column_infos = await fetch_column_list(
                user_id=user_id,
                data_id=selected_data_id,
                table_name=table_name,
                page_size=0,
                page_number=0,
                enable_page=False,
            )
            column_data = column_infos.get("data", {})
            for item in column_data.get("column_infos", []):
                # 去除无关信息
                del item["desc_gen_status"]
                del item["example_sync_status"]
                del item["statistics_sync_status"]
                del item["examples"]
                del item["statistics"]

                keys_to_delete = [key for key, value in item.items() if not value]
                for key in keys_to_delete:
                    del item[key]

            return column_data

        DB_agent = AAgent(
            name="DB_agent",
            description="智数",
            system=base_sys,
            model_config=config_data["llm"],
            tools=[get_table_info, get_column_info],
            manual_tools=[get_data],
        )

        if chat_prompt is None:
            chat_prompt = await load_prompt(path=prompt_dir, name="chat_ask_single")
            chat_prompt = chat_prompt.format(
                current_time=current_time,
                user_info=user_info,
                db_info=db_info_str,
                jargon_infos=jargon_infos,
            )
    elif len(data_list) > 1:

        def referral_data_source(
            query: str,
        ):
            """根据自然语言查询最相关的数据库并返回。
            该函数接收自然语言的文本，自动分析并推荐3个相关的数据库。

            Args:
                query: 用户输入的自然语言文本，例如 获取所有用户的姓名和邮箱 或 产品销量数据库是哪一个
            """
            pass

        # 存在多个数据源的情况
        DB_agent = AAgent(
            name="DB_agent",
            description="智数",
            system=base_sys,
            model_config=config_data["llm"],
            manual_tools=[referral_data_source],
        )

        if chat_prompt is None:
            chat_prompt = await load_prompt(path=prompt_dir, name="chat_ask_multiple")
            chat_prompt = chat_prompt.format(
                current_time=current_time,
                user_info=user_info,
            )
    else:
        # 不存在数据源的情况
        DB_agent = AAgent(
            name="DB_agent",
            description="智数",
            system=base_sys,
            model_config=config_data["llm"],
        )

        if chat_prompt is None:
            chat_prompt = await load_prompt(path=prompt_dir, name="chat_ask_none")
            chat_prompt = chat_prompt.format(
                current_time=current_time,
                user_info=user_info,
            )

    #############################

    messages = copy.deepcopy(llm_data.get("history", []))
    messages.append({"role": "system", "content": chat_prompt})
    messages.append({"role": "user", "content": user_ask})

    sql_query_str = None
    referral_data_str = None

    async for chunk in DB_agent.run(messages=messages):
        temp_id = chunk.pop("id")
        resp_raw[temp_id] = chunk
        yield resp_raw

    if resp_raw[temp_id]["ans"].get("tool_calls", None):
        for tool_call in resp_raw[temp_id]["ans"]["tool_calls"]:
            tool_call_id = tool_call["id"]

            if tool_call["function"]["name"] == "get_data":
                tool_args = tool_call["function"]["arguments"]
                sql_query_str = json_repair.loads(tool_args)

            if tool_call["function"]["name"] == "referral_data_source":
                tool_args = tool_call["function"]["arguments"]
                referral_data_str = json_repair.loads(tool_args)

    if sql_query_str and llm_data.get("selected_data_id", None):
        async for _ in _stream_get_data(
            user_id=user_id,
            user_ask=sql_query_str["query"],
            data_id=llm_data["selected_data_id"],
            qa_list=qa_list,
            llm_data=llm_data,
        ):
            if llm_data["resp_raw"]:
                yield llm_data["resp_raw"]

    elif referral_data_str:
        async for _ in await chat_select_data(
            user_id=user_id,
            data_ids=data_ids,
            dir_ids=dir_ids,
            user_ask=referral_data_str["query"],
            qa_list=qa_list,
            stream=True,
            llm_data=llm_data,
        ):
            if llm_data["resp_raw"]:
                yield llm_data["resp_raw"]


async def _stream_get_data(
    user_id: str,
    data_id: str,
    user_ask: str,
    qa_list: list,
    llm_data: dict,
):
    # async for _ in await chat_ask_refactor(
    #     user_id=user_id,
    #     user_ask=user_ask,
    #     data_id=data_id,
    #     qa_list=qa_list,
    #     stream=True,
    #     llm_data=llm_data,
    # ):
    #     if llm_data["resp_raw"]:
    #         yield llm_data["resp_raw"]

    # user_ask = llm_data["re_user_ask"]

    async for _ in await chat_select_table(
        user_id=user_id,
        user_ask=user_ask,
        data_id=data_id,
        qa_list=qa_list,
        stream=True,
        llm_data=llm_data,
    ):
        if llm_data["resp_raw"]:
            yield llm_data["resp_raw"]

    if selected_tables := llm_data.get("selected_tables", []):

        async for _ in await chat_analysis(
            user_id=user_id,
            user_ask=user_ask,
            data_id=data_id,
            qa_list=qa_list,
            tables=selected_tables,
            stream=True,
            llm_data=llm_data,
        ):
            if llm_data["resp_raw"]:
                yield llm_data["resp_raw"]

        async for _ in await chat_gen_sql(
            user_id=user_id,
            user_ask=user_ask,
            data_id=data_id,
            qa_list=qa_list,
            tables=selected_tables,
            return_ans=False,
            stream=True,
            llm_data=llm_data,
        ):
            if llm_data["resp_raw"]:
                yield llm_data["resp_raw"]

        # 新的SQL修复逻辑：每次只修复一个SQL，直到正确或达到最大修复次数
        async for _ in _refine_sql_iteratively(
            user_id=user_id,
            user_ask=user_ask,
            data_id=data_id,
            qa_list=qa_list,
            tables=selected_tables,
            stream=True,
            llm_data=llm_data,
        ):
            pass  # 流式输出已在函数内部处理

        # 直接使用修复后的SQL结果，不再进行选择
        if llm_data.get("candidate_sql") and len(llm_data["candidate_sql"]) > 0:
            final_sql_result = llm_data["candidate_sql"][0]
            llm_data["final_sql_result"] = copy.deepcopy(final_sql_result)

        async for _ in await chat_summary_sql(
            user_id=user_id,
            data_id=data_id,
            user_ask=user_ask,
            stream=True,
            llm_data=llm_data,
        ):
            if llm_data["resp_raw"]:
                yield llm_data["resp_raw"]


async def _nostream_get_data(
    user_id: str,
    data_ids: list,
    dir_ids: list,
    talk_id: str,
    user_ask: str,
    qa_list: list,
    llm_data: dict,
):
    await chat_select_data(
        user_id=user_id,
        data_ids=data_ids,
        dir_ids=dir_ids,
        user_ask=user_ask,
        qa_list=qa_list,
        stream=False,
        llm_data=llm_data,
    )

    if selected_data_id := llm_data.get("selected_data_id", ""):
        await chat_ask_refactor(
            user_id=user_id,
            user_ask=user_ask,
            data_id=selected_data_id,
            qa_list=qa_list,
            stream=False,
            llm_data=llm_data,
        )
        user_ask = llm_data["re_user_ask"]

        await chat_select_table(
            user_id=user_id,
            user_ask=user_ask,
            data_id=selected_data_id,
            qa_list=qa_list,
            stream=False,
            llm_data=llm_data,
        )

        if selected_tables := llm_data.get("selected_tables", []):
            await chat_analysis(
                user_id=user_id,
                user_ask=user_ask,
                data_id=selected_data_id,
                qa_list=qa_list,
                tables=selected_tables,
                stream=False,
                llm_data=llm_data,
            )

            await chat_gen_sql(
                user_id=user_id,
                user_ask=user_ask,
                data_id=selected_data_id,
                qa_list=qa_list,
                tables=selected_tables,
                return_ans=False,
                stream=False,
                llm_data=llm_data,
            )

            # 新的SQL修复逻辑：每次只修复一个SQL，直到正确或达到最大修复次数
            await _refine_sql_iteratively(
                user_id=user_id,
                user_ask=user_ask,
                data_id=selected_data_id,
                qa_list=qa_list,
                tables=selected_tables,
                stream=False,
                llm_data=llm_data,
            )

            # 直接使用修复后的SQL结果，不再进行选择
            if llm_data.get("candidate_sql") and len(llm_data["candidate_sql"]) > 0:
                final_sql_result = llm_data["candidate_sql"][0]
                llm_data["final_sql_result"] = copy.deepcopy(final_sql_result)
