from typing import *
from pydantic import BaseModel, Field

from src.ai_chat_data.schemas.chat_llm_schema import Talk<PERSON>, User<PERSON>k, QAList
from src.ai_data_source.schemas.data_source_schema import DataID
from src.ai_data_source.schemas.data_source_schema import DataDirIDs
from src.ai_data_source.schemas.semantic_layer_schema import TableNames


class Stream(BaseModel):
    stream: bool = Field(
        True,
        title="流式输出",
        description="是否使用流式输出",
        examples=[True],
    )


class SqlList(BaseModel):
    sql_list: list = Field(
        ...,
        title="sql列表",
        description="可选的sql列表",
    )


class SqlResult(BaseModel):
    sql_result: dict = Field(
        ...,
        title="sql结果",
        description="单个sql运行结果",
    )


class ChatSelectData(DataDirIDs, UserAsk, QAList, Stream):
    pass


class ChatAskRefactor(UserAsk, QAList, DataID, Stream):
    pass


class ChatSelectTable(UserAsk, QAList, DataID, Stream):
    pass


class ChatAnalysis(UserAsk, QAList, DataID, TableNames, Stream):
    pass


class ChatGenSql(UserAsk, QAList, DataID, TableNames, Stream):
    pass


class ChatRfSql(UserAsk, QAList, DataID, TableNames, SqlList, Stream):
    pass


class ChatRfSqlSingle(UserAsk, QAList, DataID, TableNames, SqlResult, Stream):
    pass


class ChatSelectSql(UserAsk, SqlList, Stream):
    pass


class ChatData(DataDirIDs, UserAsk, QAList, TalkID, Stream):
    pass
